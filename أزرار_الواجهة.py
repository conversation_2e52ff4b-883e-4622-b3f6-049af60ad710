from قاعدة_البيانات import*
from ستايل import*
from الدوال_الأساسية import*
from التحديثات import*
from الإعدادات_العامة import*
from متغيرات import*

# عرض البطاقات العصرية (لجميع الأقسام)
from نظام_البطاقات import ModernCardsContainer



class DraggableToolBar(QToolBar):
    def __init__(self, title, parent=None):
        super().__init__(title, parent)
        self.setMovable(True)
        self.setFloatable(True)
        self.setAllowedAreas(Qt.AllToolBarAreas)
        self.setObjectName("DraggableToolBar")
        self.parent = parent

        # Set initial position to top
        self.setOrientation(Qt.Horizontal)

        # Create layout for toolbar contents
        self.main_widget = QWidget()
        self.main_layout = QHBoxLayout(self.main_widget)
        self.main_layout.setContentsMargins(3, 0, 3, 0)
        self.main_layout.setSpacing(5)

        # Add the widget to the toolbar
        self.addWidget(self.main_widget)

        # Create menus
        self._create_menus()

        # Initialize components in the correct order for proper layout
        self._setup_menu_buttons()      # أزرار القائمة على اليمين
        self._setup_search_bar()        # شريط البحث في المنتصف
        self._setup_notifications()     # زر الإشعارات بعد شريط البحث
        self._setup_username_display()  # عرض اسم المستخدم بعد زر الإشعارات
        self._setup_datetime_display()  # عرض الوقت والتاريخ في أقصى اليسار


        # Timer for updating date/time
        self.datetime_timer = QTimer(self)
        self.datetime_timer.timeout.connect(self._update_datetime)
        self.datetime_timer.start(1000)  # Update every second

        # Apply stylesheet
        self._apply_stylesheet()

    def _create_menus(self):
        """Create all menus for the toolbar"""
        # إنشاء القوائم الرئيسية
        self.file_menu = QMenu("ملف")
        self.file_menu.setObjectName("ملف")

        self.customize_menu = QMenu("تخصيص")
        self.customize_menu.setObjectName("تخصيص")

        self.security_menu = QMenu("الحماية")
        self.security_menu.setObjectName("الحماية")

        self.help_menu = QMenu("مساعدة")
        self.help_menu.setObjectName("مساعدة")

        self.info_menu = QMenu("معلومات")
        self.info_menu.setObjectName("معلومات")

        self.accounting_menu = QMenu("محاسبة")
        self.accounting_menu.setObjectName("محاسبة")

        # Set layout direction for all menus
        for menu in [self.file_menu, self.customize_menu, self.security_menu,
                    self.help_menu, self.info_menu, self.accounting_menu]:
            menu.setLayoutDirection(Qt.RightToLeft)

    def _setup_menu_buttons(self):
        """Setup menu buttons on the toolbar"""
        menu_widget = QWidget()
        menu_layout = QHBoxLayout(menu_widget)
        menu_layout.setContentsMargins(0, 0, 0, 0)
        menu_layout.setSpacing(3)

        # Create menu buttons
        self.file_btn = QPushButton("ملف")
        self.file_btn.setObjectName("ToolbarMenuButton")

        self.customize_btn = QPushButton("تخصيص")
        self.customize_btn.setObjectName("ToolbarMenuButton")

        self.security_btn = QPushButton("الحماية")
        self.security_btn.setObjectName("ToolbarMenuButton")

        self.help_btn = QPushButton("مساعدة")
        self.help_btn.setObjectName("ToolbarMenuButton")

        self.info_btn = QPushButton("معلومات")
        self.info_btn.setObjectName("ToolbarMenuButton")

        self.accounting_btn = QPushButton("محاسبة")
        self.accounting_btn.setObjectName("ToolbarMenuButton")

        # Add buttons to layout (عكس الترتيب من اليمين إلى اليسار)
        menu_layout.addWidget(self.file_btn)
        menu_layout.addWidget(self.customize_btn)
        menu_layout.addWidget(self.security_btn)
        menu_layout.addWidget(self.help_btn)
        menu_layout.addWidget(self.info_btn)
        menu_layout.addWidget(self.accounting_btn)

        # Add to main layout
        self.main_layout.addWidget(menu_widget)

    def _setup_search_bar(self):
        """Setup search bar on the toolbar"""
        # إنشاء حاوية لشريط البحث
        search_container = QWidget()
        search_layout = QHBoxLayout(search_container)
        search_layout.setContentsMargins(0, 0, 0, 0)

        # إضافة مساحة مرنة قبل شريط البحث لدفعه للمنتصف
        search_layout.addStretch(1)

        # إنشاء شريط البحث
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("بحث...")
        self.search_input.setMinimumWidth(250)
        self.search_input.setMaximumWidth(350)
        self.search_input.setObjectName("ToolbarSearchInput")

        # إضافة أيقونة البحث
        search_icon = QIcon(أيقونة_بحث)
        self.search_input.addAction(search_icon, QLineEdit.LeadingPosition)

        # إضافة شريط البحث إلى الحاوية
        search_layout.addWidget(self.search_input)

        # إضافة مساحة مرنة بعد شريط البحث لإبقائه في المنتصف
        #search_layout.addStretch(1)

        # إضافة الحاوية إلى التخطيط الرئيسي
        self.main_layout.addWidget(search_container)

    def _setup_datetime_display(self):
        """Setup date and time display on the toolbar"""
        self.datetime_label = QLabel()
        self.datetime_label.setObjectName("ToolbarDateTimeLabel")
        self._update_datetime()  # Initial update

        self.main_layout.addWidget(self.datetime_label)

    def _update_datetime(self):
        """Update the date and time display"""
        now = datetime.now()

        # Get Arabic day name
        day_of_week = now.weekday() + 1  # Convert from 0-6 to 1-7
        arabic_days = {
            1: "الإثنين",
            2: "الثلاثاء",
            3: "الأربعاء",
            4: "الخميس",
            5: "الجمعة",
            6: "السبت",
            7: "الأحد"
        }
        arabic_day = arabic_days.get(day_of_week, "")

        # Format date
        date_str = now.strftime("%Y-%m-%d")

        # Format time with Arabic AM/PM
        hour = now.hour
        am_pm = "ص" if hour < 12 else "م"

        # Convert to 12-hour format
        hour_12 = hour % 12
        if hour_12 == 0:
            hour_12 = 12

        time_str = f"{hour_12}:{now.minute:02d}:{now.second:02d} {am_pm}"

        # Combine all parts with separators
        self.datetime_label.setText(f"{arabic_day} | {date_str} | {time_str}")

    def _setup_username_display(self):
        """Setup username display on the toolbar"""
        # Get account type from settings
        account_type = settings.value("account_type", "admin")

        self.username_label = QLabel(f"👤 {account_type}")
        self.username_label.setObjectName("ToolbarUsernameLabel")

        self.main_layout.addWidget(self.username_label)

    def _setup_notifications(self):
        """Setup notifications bell on the toolbar"""
        self.notification_btn = QPushButton()
        self.notification_btn.setObjectName("ToolbarNotificationButton")

        # Set bell icon
        bell_icon_path = os.path.join(icons_dir, 'check.png')
        if os.path.exists(bell_icon_path):
            self.notification_btn.setIcon(QIcon(bell_icon_path))
        else:
            self.notification_btn.setText("🔔")

        self.notification_btn.setIconSize(QSize(20, 30))
        #self.notification_btn.setFixedSize(20, 18)

        # Add notification button after the search bar
        # We'll simply add it to the main layout since the order is now controlled
        # by the initialization sequence in __init__
        self.main_layout.addWidget(self.notification_btn)

    def addMenuButton(self, menu, text, function, icon_path=None):
        """Add a button to a menu"""
        button = QPushButton(text)
        button.clicked.connect(function)
        button.setFixedWidth(220)  # تحديد عرض ثابت للزر
        if icon_path:
            button.setIcon(QIcon(icon_path))  # تعيين الأيقونة من المسار المحدد
            button.setIconSize(QSize(20, 20))  # تحديد حجم الأيقونة
        action = QWidgetAction(menu)
        action.setDefaultWidget(button)
        menu.addAction(action)

    def _apply_stylesheet(self):
        """Apply stylesheet to the toolbar"""
        self.setStyleSheet("""
            QToolBar {
                background: qlineargradient(
                    spread:pad,
                    x1:0, y1:0,
                    x2:1, y2:0,
                    stop:0 #24384a,
                    stop:1 #1b3459
                );
                border-bottom: 1px solid #5a4765;
                padding: 2px;
                min-height: 35px;
                max-height: 40px;
                font-weight: bold;
            }

            #ToolbarMenuButton {
                background-color: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
                color: white;
                border: none;
                padding: 2px 8px;
                font-weight: bold;
                font-size: 14px;
                font-family:'Janna LT';
                border-radius: 3px;
                margin: 0 2px;
                max-height: 25px;
            }

            #ToolbarMenuButton:hover {
                background-color: rgba(255, 255, 255, 0.2);
                border-bottom: 3px solid #f39c12;
            }

            #ToolbarSearchInput {
                border: 1px solid #3498db;
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 10px;
                padding: 2px 8px;
                background-color: rgba(255, 255, 255, 0.1);
                color: #ffffff;
                font-weight: bold;
                min-height: 10px;
                max-height: 25px;
                font-size: 14px;
                font-family:'Janna LT';

            }

            #ToolbarSearchInput:focus {
                border:2px solid #f39c12;
                background-color: rgba(255, 255, 255, 0.2);
            }

            #ToolbarDateTimeLabel, #ToolbarUsernameLabel {
                padding: 2px 8px;
                font-weight: bold;
                color: white;
                background-color: rgba(255, 255, 255, 0.1);
                border-radius: 5px;
                margin: 0 3px;
                border: 1px solid rgba(255, 255, 255, 0.2);
                max-height: 25px;
                font-size: 14px;
                font-family:'Janna LT';
            }

            #ToolbarNotificationButton {
                background-color: rgba(255, 255, 255, 0.1);
                border: 1px solid rgba(255, 255, 255, 0.2);
                border-radius: 5px;
                padding: 2px 5px;
                margin: 0 3px;

                max-height: 25px;
                font-size: 14px;
                font-family:'Janna LT';

            }

            #ToolbarNotificationButton:hover {
                background-color: rgba(255, 255, 255, 0.2);
                border:2px solid #f39c12;
            }

            QMenu {
                background-color: #2c3e50;
                border: 1px solid #5d6d7e;
                border-radius: 6px;
                padding: 5px;
                color: white;
                font-size: 14px;
                font-weight: bold;
                min-width: 180px;

                border-bottom: 2px solid #6B1F659C;
            }

            QMenu::item {
                padding: 8px 25px 8px 15px;
                border-radius: 4px;
                margin: 3px 5px;
                font-weight: bold;

            }

            QMenu::item:hover {
                background-color: #3e4a5b;
                border: 1px solid #5d6d7e;
            }

            QMenu::item:selected {
                background-color: #4a5c70;
                color: white;
                border: 1px solid #7cb4e2;
            }

            QMenu::separator {
                height: 1px;
                background-color: #5d6d7e;
                margin: 5px 10px;

            }
        """)

class SideMenuButton(QPushButton):
    def __init__(self, icon_path, text, parent=None):
        super().__init__("", parent)
        #self.setIconSize(QSize(32, 32))
        #self.setLayoutDirection(Qt.RightToLeft)
        layout = QVBoxLayout(self)
        layout.setAlignment(Qt.AlignTop | Qt.AlignCenter) # Align contents top-center

        icon_label = QLabel()
        if os.path.exists(icon_path):
             pixmap = QPixmap(icon_path).scaled(32, 32, Qt.KeepAspectRatio, Qt.SmoothTransformation)
             icon_label.setPixmap(pixmap)
        else:
             icon_label.setText("?")
             #icon_label.setFont(QFont("Janna LT", 16))

        icon_label.setAlignment(Qt.AlignCenter)

        text_label = QLabel(text)
        text_label.setAlignment(Qt.AlignCenter)
        #text_label.setFont(QFont("Janna LT", 10)) # خط أصغر للنص
        #خلّي النص داخل  يلف (يُنزل للسطر اللي بعده) لما يكون طويل وما يكفي في السطر.
        text_label.setWordWrap(True)

        layout.addWidget(icon_label)
        layout.addWidget(text_label)
        layout.addStretch()

        self.setFixedSize(100, 80) # حجم ثابت للزر
        self.setObjectName("SideMenuButton")

        layout.setSpacing(2)
        layout.setContentsMargins(5, 6, 5, 2)
        text_label.setMinimumHeight(text_label.fontMetrics().height())
        self._apply_stylesheet()

    def _apply_stylesheet(self):
        """تطبيق ستايل شيت على الزر"""
        apply_stylesheet(self)


# ايقونه فوق النص للازرار المخصصة
class CustomActionButton(QPushButton):
    def __init__(self, icon_path, text, parent=None):
        super().__init__("", parent)
        #self.setIconSize(QSize(32, 32))
        self.setObjectName("CustomActionButton")

        layout = QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(2)
        layout.setAlignment(Qt.AlignCenter)

        icon_label = QLabel()
        icon_size = 38 # Smaller icon size for in-page buttons
        if os.path.exists(icon_path):
            pixmap = QPixmap(icon_path).scaled(icon_size, icon_size, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            icon_label.setPixmap(pixmap)
        else:
            icon_label.setText("?")
            #icon_label.setFont(QFont("Arial", 12))

        icon_label.setAlignment(Qt.AlignCenter)

        text_label = QLabel(text)
        text_label.setAlignment(Qt.AlignCenter)
        #text_label.setFont(QFont("Janna LT", 11))
        text_label.setWordWrap(True)
        text_label.setMinimumHeight(text_label.fontMetrics().height())

        layout.addWidget(icon_label)
        layout.addWidget(text_label)

        #self.setFixedSize(100, 80)
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        apply_stylesheet(self)

#بوكسات الاحصائية
class StatBox(QFrame):
    def __init__(self, title, value, parent=None):
        super().__init__(parent)
        self.setFrameShape(QFrame.StyledPanel)
        self.setFrameShadow(QFrame.Raised)
        self.setObjectName("StatBox")
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed) # Allow horizontal stretch
        self.setMinimumHeight(60) # Ensure minimum height

        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 5, 10, 5) # هوامش داخلية
        layout.setSpacing(3) # تباعد بين العناصر
        layout.setAlignment(Qt.AlignCenter) # Center content vertically

        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setObjectName("StatTitle")
        title_label.setWordWrap(True) # Allow wrapping if title is long
        # Use fallback font
        font_title = QFont("Janna LT", 12)
        title_label.setFont(font_title)

        value_label = QLabel(str(value))

        value_label.setFont(QFont("Arial", 14, QFont.Bold))
        value_label.setAlignment(Qt.AlignCenter)
        value_label.setObjectName("StatValue")

        layout.addWidget(title_label)
        layout.addWidget(value_label)


#إنشاء صفحات لكل قسم في
def create_sections(self):
    self.sections = {}

    for section_name in self.interactive_sections:
        page = QWidget()
        page.setObjectName(f"{section_name.replace(' ', '_')}_Page")
        page_layout = QVBoxLayout(page)
        page_layout.setContentsMargins(10, 10, 10, 10)
        page_layout.setSpacing(5)
        page_layout.setAlignment(Qt.AlignTop)

        # --- Header Row: Title, Std Actions, Search, Year ---
        header_row_frame = QFrame()
        header_row_frame.setObjectName("SectionHeaderRowFrame")

        header_row_layout = QHBoxLayout(header_row_frame)
        header_row_layout.setContentsMargins(0, 0, 0, 0)
        header_row_layout.setSpacing(5)
        header_row_layout.setAlignment(Qt.AlignVCenter | Qt.AlignRight)

        # Section Title (Right)
        section_title_label = QLabel(f"{section_name}")
        section_title_label.setObjectName("SectionTitleLabel")
        section_title_label.setAlignment(Qt.AlignCenter)
        # تعيين نفس ارتفاع البطاقات وتلوين البوردر الأيسر
        section_title_label.setMinimumHeight(46)
        section_title_label.setMaximumHeight(100)
        section_title_label.setProperty("border_type", "stat_border_blue")  # لون أزرق للعنوان
        header_row_layout.addWidget(section_title_label)

        # Spacer to push search/year to the left
        #header_row_layout.addStretch()

        # Year Selector and Search Input (Left)
        controls_layout = QHBoxLayout()
        controls_layout.setSpacing(2)
        controls_layout.setAlignment(Qt.AlignVCenter | Qt.AlignLeft)

        header_row_layout.addLayout(controls_layout)
        page_layout.addWidget(header_row_frame)

        # --- إنشاء تخطيط عمودي للتحكمات الجانبية (بحث + سنة + حالة) ---------------------------------------
        controls_layout = QHBoxLayout()
        controls_layout.setSpacing(2)
        controls_layout.setAlignment(Qt.AlignVCenter | Qt.AlignLeft)
        # نضيف كل شيء إلى الهيدر
        header_row_layout.addLayout(controls_layout)

        # إنشاء كومبو السنة منفصل لكل قسم
        year_combo = QComboBox()
        year_combo.setObjectName("YearComboBox")
        self.populate_years(year_combo)
        year_combo.currentIndexChanged.connect(lambda index, sec_name=section_name: self.change_year(index, sec_name))
        ComboBox_Center_item(year_combo)
        # تعيين نفس ارتفاع البطاقات وتلوين البوردر الأيسر
        year_combo.setMinimumHeight(46)
        year_combo.setMaximumHeight(100)
        year_combo.setProperty("border_type", "stat_border_green")  # لون أخضر للسنة
        year_combo.setStyleSheet("""
            QComboBox {
                min-width: 50px;
            }
            """)
        controls_layout.addWidget(year_combo)

        # --- احصاءات Stat Boxes ---
        stat_names = []
        if section_name == "المشاريع":
                stat_names = ["مشاريع قيد الإنجاز", "الوارد الشهري", "الوارد السنوي", "إجمالي الباقي"]
        elif section_name == "المقاولات":
                stat_names = ["مقاولات قيد الإنجاز", "الوارد الشهري", "الوارد السنوي", "إجمالي الباقي"]
        elif section_name == "الحسابات":
                stat_names = ["مصروفات الشهر", "مصروفات السنة", "إجمالي المصروفات"]
        elif section_name == "الموظفين":
                stat_names = ["عدد الموظفين", "إجمالي الرصيد", "إجمالي السحب"]
        elif section_name == "العملاء":
                stat_names = ["إجمالي العملاء", "عملاء جدد هذا الشهر", "عملاء لهم مشاريع نشطة"]

        elif section_name == "العقارات":
                stat_names = ["عقارات متاحة", "عقارات تم بيعها", "إجمالي قيمة العقارات المتاحة", "عقارات جديدة هذا الشهر"]

        elif section_name == "التدريب":
                stat_names = ["دورات قيد التسجيل", "دورات جارية", "إجمالي الإيرادات", "إجمالي المشاركين"]

        elif section_name == "التقارير":
                stat_names = ["دورات قيد التسجيل", "دورات جارية", "إجمالي الإيرادات", "إجمالي المشاركين"]

        current_stat_boxes = {}
        # Cycle through STAT_BORDER_TYPES for different borders
        for i, name in enumerate(stat_names):
                stat_box = StatBox(name, "جار التحميل...")
                # Assign a dynamic property to determine border color
                border_type = STAT_BORDER_TYPES[i % len(STAT_BORDER_TYPES)]
                stat_box.setProperty("border_type", border_type)
                current_stat_boxes[name] = stat_box
                controls_layout.addWidget(stat_box)


        # إنشاء فلتر التصنيف لجميع الأقسام التي تحتوي على عمود التصنيف
        sections_with_classification = ["المشاريع", "المقاولات", "العملاء", "الحسابات", "الموظفين", "العقارات", "التدريب", "التقارير"]
        if section_name in sections_with_classification:
            classification_filter_combo = QComboBox()
            classification_filter_combo.setObjectName("ClassificationFilterComboBox")
            self.populate_classification_filter(classification_filter_combo, section_name)
            ComboBox_Center_item(classification_filter_combo)
            # تعيين نفس ارتفاع البطاقات وتلوين البوردر الأيسر
            classification_filter_combo.setMinimumHeight(46)
            classification_filter_combo.setMaximumHeight(100)
            classification_filter_combo.setProperty("border_type", "stat_border_purple")  # لون برتقالي للتصنيف
            classification_filter_combo.setStyleSheet("""
                QComboBox {
                    min-width: 85px;
                }
                """)
            classification_filter_combo.currentTextChanged.connect(lambda text, sec=section_name: self.filter_table_by_classification(sec, text))
            controls_layout.addWidget(classification_filter_combo)
        else:
            classification_filter_combo = None

        # إنشاء فلتر الحالة منفصل لكل قسم
        filter_combo = QComboBox()
        filter_combo.setObjectName("StatusFilterComboBox")
        self.populate_status_filter(filter_combo, section_name)
        ComboBox_Center_item(filter_combo)
        # تعيين نفس ارتفاع البطاقات وتلوين البوردر الأيسر
        filter_combo.setMinimumHeight(46)
        filter_combo.setMaximumHeight(100)
        filter_combo.setProperty("border_type", "stat_border_lime")  # لون أحمر للحالة
        filter_combo.setStyleSheet("""
            QComboBox {
                min-width: 85px;
            }
            """)
        filter_combo.currentTextChanged.connect(lambda text, sec=section_name: self.filter_table(sec, text))
        controls_layout.addWidget(filter_combo)


        # --- ازرار اضافية Custom Action Buttons ---
        custom_actions_container = QWidget()
        custom_actions_layout = QHBoxLayout(custom_actions_container)
        custom_actions_layout.setContentsMargins(0, 0, 0, 0)
        custom_actions_layout.setSpacing(8)
        custom_actions_layout.setAlignment(Qt.AlignRight | Qt.AlignTop)

        section_custom_config = CUSTOM_ACTIONS_CONFIG.get(section_name, [])
        custom_buttons_list = []

        if not section_custom_config:
            custom_actions_container.hide()
        else:
            for item in section_custom_config:
                if len(item) >= 4:  # إذا كان العنصر يحتوي على 4 عناصر أو أكثر (النص، الأيقونة، اسم الإجراء، نوع البوردر)
                    btn_text, btn_icon, action_name, border_type = item
                    btn = CustomActionButton(btn_icon, btn_text)
                    # تعيين خصائص الزر
                    btn.setProperty("action", action_name)
                    btn.setProperty("border_type", border_type)
                elif len(item) == 3:  # إذا كان العنصر يحتوي على 3 عناصر فقط (النص، الأيقونة، اسم الإجراء)
                    btn_text, btn_icon, action_name = item
                    btn = CustomActionButton(btn_icon, btn_text)
                    # تعيين خاصية الإجراء فقط
                    btn.setProperty("action", action_name)

                # ربط الزر بمعالج الإجراء المخصص
                btn.clicked.connect(lambda checked=False, act=action_name, sec=section_name: self.handle_custom_action(act, sec))
                custom_actions_layout.addWidget(btn)
                custom_buttons_list.append(btn) # إضافة إلى القائمة

            #custom_actions_layout.addStretch() # Push custom buttons to the right

        # Scroll Area to wrap the custom action buttons
        custom_actions_scroll = QScrollArea()
        custom_actions_scroll.verticalScrollBar().setObjectName("customScrollBar")
        custom_actions_scroll.setWidgetResizable(True)
        custom_actions_scroll.setHorizontalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        custom_actions_scroll.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        custom_actions_scroll.setFrameShape(QScrollArea.NoFrame)
        custom_actions_scroll.setWidget(custom_actions_container)
        custom_actions_scroll.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        custom_actions_scroll.setFixedHeight(110)  # عدل الارتفاع حسب حجم الأزرار
        custom_actions_scroll.setStyleSheet("background-color: transparent;")


        #page_layout.addWidget(custom_actions_frame)
        page_layout.addWidget(custom_actions_scroll)

        # البحث يبقى في تخطيط أفقي لحاله
        search_layout = QHBoxLayout()
        search_input = QLineEdit()
        search_input.setAlignment(Qt.AlignCenter)
        search_input.setPlaceholderText(f"بحث في {section_name}...")
        search_input.setObjectName("SearchInput")
        search_input.setStyleSheet("""
            QLineEdit {
                min-width: 80px;
                min-height: 40px;

            }
            """)
        search_input.textChanged.connect(lambda text, sec_name=section_name: self.search_data(text, sec_name))
        # إضافة أيقونة البحث
        search_icon = QIcon(أيقونة_بحث)
        search_input.addAction(search_icon, QLineEdit.LeadingPosition)

        #search_layout.addWidget(search_input, 1)
        #controls_layout.addLayout(search_layout, 1)

        # تم نقل كومبو السنة والتصنيف والحالة إلى أعلى قبل بطاقات المعلومات


        # --- Empty State Widget (Logo + Button) ---
        # This widget will be shown when the table is empty
        empty_state_widget = QWidget()
        empty_state_widget.setObjectName("EmptyStateWidget")
        empty_state_layout = QVBoxLayout(empty_state_widget)
        empty_state_layout.setContentsMargins(0, 0, 0, 0)
        empty_state_layout.setSpacing(0)
        empty_state_layout.setAlignment(Qt.AlignCenter) # Center contents vertically and horizontally

        # # استخدام QGridLayout بدلاً من QVBoxLayout
        # empty_state_layout = QGridLayout(empty_state_widget)
        # empty_state_layout.setContentsMargins(20, 20, 20, 20) # هوامش حول الشبكة كلها
        # empty_state_layout.setSpacing(25) # المسافات بين الأزرار
        # empty_state_layout.setAlignment(Qt.AlignCenter) # محاذاة الشبكة في المنتصف

        empty_state_logo_label = QLabel()
        empty_state_logo_label.setObjectName("EmptyStateLogo")
        logo_size_large = 350 # Larger logo for empty state
        if os.path.exists(أيقونة_الشعار):
            pixmap = QPixmap(أيقونة_الشعار).scaled(logo_size_large, logo_size_large, Qt.KeepAspectRatio, Qt.SmoothTransformation)
            empty_state_logo_label.setPixmap(pixmap)
        else:
                empty_state_logo_label.setText(f"{self.company_name}\nLogo")
                empty_state_logo_label.setAlignment(Qt.AlignCenter)
                empty_state_logo_label.setFont(QFont("Janna LT", 24, QFont.Bold))

        empty_state_layout.addWidget(empty_state_logo_label)

        # Text under logo
        version_label = QLabel(f"منظومة المهندس V{CURRENT_VERSION}")
        version_label.setAlignment(Qt.AlignCenter)
        #version_label.setFont(QFont("Janna LT", 50, QFont.Bold))
        version_label.setStyleSheet("""
        QLabel {
            background-color: transparent;
            color: #cccccc;
            border: none;
            padding: 10px 10px; /* Larger padding */
            border-radius: 8px; /* More rounded corners */
            font-size: 20pt; /* Larger font */
            font-weight: bold;
            min-height: 20;
            max-height: 80;
            font-family: "Janna LT"; /* اسم الخط */
        }"""
        )

        empty_state_layout.addWidget(version_label)

        if section_name in section_labels:
            label = section_labels[section_name]
            empty_state_add_button = QPushButton(f"           إضافة {label} جديد")

        empty_state_add_button.setObjectName("EmptyStateAddButton")

        empty_state_add_button.setIcon(QIcon(أيقونة_إضافة))
        empty_state_add_button.setIconSize(QSize(24, 24))

        empty_state_add_button.setFont(QFont("Janna LT", 16, QFont.Bold))
        # ضبط التنسيق باستخدام StyleSheet
        empty_state_add_button.setStyleSheet("""
            QPushButton {
                padding: 8px 16px;
                text-align: center;
                qproperty-iconSize: 24px;
            }
            QPushButton::icon {
                padding-left: 04px; /* تقلل المسافة بين الأيقونة والنص */
            }
        """)
        # تأكيد على المحاذاة (احتياط)
        empty_state_add_button.setLayoutDirection(Qt.RightToLeft)  # مهم لو تبي الأيقونة يمين
        empty_state_add_button.clicked.connect(lambda checked=False, sec_name=section_name: self.handle_action_button("اضافة", sec_name))


        #empty_state_layout.addStretch() # Push logo/button to center


        empty_state_layout.addWidget(empty_state_add_button)
        #empty_state_layout.addStretch() # Push logo/button to center

        # Initially hide the empty state widget
        empty_state_widget.hide()
        # Set a size policy so it can expand if needed, but respects layout minimums
        # empty_state_widget.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)


        page_layout.addWidget(empty_state_widget, 1) # Add empty state widget, give it stretch factor


        # --- Bottom Area: The Table and Cards View -------------------------------------------------
        # إنشاء StackedWidget للتبديل بين عرض الجدول والبطاقات (منفصل لكل قسم)
        view_stack = QStackedWidget()  # إنشاء view_stack منفصل لكل قسم
        view_stack.setObjectName("ViewStack")

        # عرض الجدول (الافتراضي)
        table = QTableWidget()
        table.setObjectName("DataTable")
        columns = TABLE_COLUMNS.get(section_name, []) # Get columns from the map
        self._setup_table(table, columns) # Call setup later after filling data
        view_stack.addWidget(table)

        

        # تحديد نوع البطاقة حسب القسم
        card_type_mapping = {
            "المشاريع": "project",
            "المقاولات": "project",  # المقاولات تستخدم نفس نوع بطاقة المشاريع
            "العملاء": "client",
            "الموظفين": "employee",
            "المصروفات": "expense",
            "العقارات": "realestate",
            "التدريب": "training"
        }

        card_type = card_type_mapping.get(section_name, "project")
        cards_view = ModernCardsContainer(card_type)
        view_stack.addWidget(cards_view)

        # إضافة زر التبديل الفردي لكل قسم
        view_toggle_btn = QPushButton()
        view_toggle_btn.setObjectName("ViewToggleBtn")
        view_toggle_btn.setFixedSize(120, 46)
        view_toggle_btn.setProperty("border_type", "stat_border_orange")

        # تحديد النص الأولي حسب التفضيل المحفوظ
        # استخدام الافتراضي إذا لم تكن الدالة متوفرة بعد
        try:
            preferred_view = self.get_section_view_preference(section_name)
        except AttributeError:
            # استخدام الافتراضي المؤقت
            preferred_view = "table" if section_name == "الحسابات" else "cards"
        if preferred_view == "cards":
            view_toggle_btn.setText("📊 جدول")
            view_toggle_btn.setToolTip(f"التبديل إلى عرض الجدول لقسم {section_name}")
        else:
            view_toggle_btn.setText("🎴 بطاقات")
            view_toggle_btn.setToolTip(f"التبديل إلى عرض البطاقات لقسم {section_name}")

        view_toggle_btn.clicked.connect(lambda checked=False, sec_name=section_name: self.toggle_section_view_and_update_button(sec_name))
        controls_layout.addWidget(view_toggle_btn)

        page_layout.addWidget(view_stack, 1) # Set stretch factor to 1 so view takes remaining space

        self.main_content_area.addWidget(page)

        # تخزين المعلومات الخاصة بهذا القسم/الصفحة
        section_data = {
            "page": page,
            "table": table,
            "view_stack": view_stack,  # Reference to the view stack (منفصل لكل قسم)
            "view_toggle_btn": view_toggle_btn,  # Reference to the toggle button
            "stats": current_stat_boxes,
            "search_input": search_input, # Reference to THIS section's search input
            "year_combo": year_combo,     # Reference to THIS section's year combo (منفصل لكل قسم)
            "filter_combo": filter_combo, # Reference to filter combo (منفصل لكل قسم)
            "classification_filter_combo": classification_filter_combo, # Reference to classification filter combo (منفصل لكل قسم)
            "custom_buttons": custom_buttons_list, # List of THIS section's custom buttons
            "title_label": section_title_label, # Reference to THIS section's title
            "empty_state_widget": empty_state_widget, # Reference to the empty state widget
            "empty_state_add_button": empty_state_add_button, # Reference to the button inside empty state
            "current_view": "table"  # Track current view (table or cards)
        }

        # تم حفظ classification_filter_combo بالفعل في section_data أعلاه
        # لا حاجة لإعادة تعيينه هنا

        self.sections[section_name] = section_data




    # Connect year combo box signals after all sections are created
    # This ensures that the lambda correctly captures the section_name
    for section_name, section_info in self.sections.items():
            section_info["year_combo"].currentIndexChanged.connect(
                lambda index, sec_name=section_name: self.change_year(index, sec_name)
            )

    center_all_widgets(page)


# الانتقال إلى قسم معين وتحميل بياناته من قاعدة البيانات
def show_section(self, section_name):
    # تحديث حالة الأزرار - إزالة خاصية active من جميع الأزرار
    for button in self.findChildren(SideMenuButton):
        button.setProperty("active", "false")
        button.style().unpolish(button)
        button.style().polish(button)
        button.update()

    # تعيين خاصية active للزر الحالي
    for button in self.findChildren(SideMenuButton):
        if button.property("section") == section_name:
            button.setProperty("active", "true")
            button.style().unpolish(button)
            button.style().polish(button)
            button.update()
            break

    if section_name in self.sections:
        section_info = self.sections[section_name]
        self.main_content_area.setCurrentWidget(section_info["page"])


        self.setWindowTitle(f"{self.company_name} - {section_name}")

        selected_year_widget = section_info["year_combo"]
        selected_year = selected_year_widget.currentText()

        data = self._load_data_from_db(section_info["table"], section_name, selected_year)

        if data is None or not data:
            data = []


        self._update_stats(section_info["stats"], section_name, selected_year)

        section_info["search_input"].clear()

        if "filter_combo" in section_info and section_info["filter_combo"].count() > 0:
            # تحديد النص الصحيح حسب نوع القسم
            if section_name in ["المشاريع", "المقاولات", "العقارات", "التدريب"]:
                section_info["filter_combo"].setCurrentText("كل الحالات")
            else:
                section_info["filter_combo"].setCurrentText("كل التصنيفات")

        # Reset classification filter for all sections that have it
        if "classification_filter_combo" in section_info and section_info["classification_filter_combo"] is not None and section_info["classification_filter_combo"].count() > 0:
            section_info["classification_filter_combo"].setCurrentText("كل التصنيفات")

        # Update remaining time for projects and contracting sections
        if section_name in ["المشاريع", "المقاولات"]:
            for row in data:
                # التحقق من حالة المشروع - التحديث التلقائي فقط للمشاريع قيد الإنجاز
                project_status = row.get("الحالة", "")
                current_remaining_time = row.get("الوقت_المتبقي", "")
                

                # تحديث الوقت المتبقي فقط إذا كانت الحالة "قيد الإنجاز"
                # ولا يحتوي الوقت المتبقي على نصوص خاصة
                if (project_status == "قيد الإنجاز" and
                    "تم الإنجاز" not in str(current_remaining_time) and
                    "متوقف" not in str(current_remaining_time) and
                    "معلق" not in str(current_remaining_time)):

                    delivery_date = row["تاريخ_التسليم"]
                    if isinstance(delivery_date, date):
                        # Convert datetime.date to QDate
                        delivery_date_qdate = QDate(delivery_date.year, delivery_date.month, delivery_date.day)
                    elif isinstance(delivery_date, str) and delivery_date:
                        # Handle string dates
                        delivery_date_qdate = QDate.fromString(delivery_date, Qt.ISODate)
                    else:
                        # Handle None or invalid dates
                        delivery_date_qdate = QDate()

                    current_date = QDate.currentDate()
                    remaining_days = current_date.daysTo(delivery_date_qdate) if delivery_date_qdate.isValid() else 0

                    if remaining_days > 0:
                        row["الوقت_المتبقي"] = f"متبقي {remaining_days} يوم"
                    elif remaining_days == 0:
                        row["الوقت_المتبقي"] = f"اليوم"
                    else:
                        # إذا كان أقل من 0، نعرض "متأخر" مع عدد الأيام المتأخرة
                        delayed_days = abs(remaining_days)
                        row["الوقت_المتبقي"] = f"متأخر {delayed_days} يوم"

            # Update table to reflect changes in الوقت_المتبقي
            if data:
                table = section_info["table"]
                try:
                    # Find column index for الوقت_المتبقي
                    col_index = -1
                    for i in range(table.columnCount()):
                        if table.horizontalHeaderItem(i).text().strip() == "الوقت المتبقي":
                            col_index = i
                            break

                    if col_index == -1:
                        # If not found by header text, try to find by data keys
                        try:
                            col_index = list(data[0].keys()).index("الوقت_المتبقي")
                        except ValueError:
                            print("Error: Column الوقت_المتبقي not found in table headers or data keys")

                    # Update the الوقت_المتبقي column in the table
                    for row_index, row_data in enumerate(data):
                        if col_index >= 0:
                            item = QTableWidgetItem(row_data["الوقت_المتبقي"])
                            item.setTextAlignment(Qt.AlignCenter)
                            table.setItem(row_index, col_index, item)
                except Exception as e:
                    print(f"Error updating الوقت_المتبقي column: {e}")

        # Emit signal for data update (optional)
            self.data_updated.emit(section_name)

        # الحصول على الجدول من معلومات القسم
        table = section_info["table"]
        self.colorize_cells(table, section_name)

        # استعادة تفضيل العرض المحفوظ للقسم
        try:
            preferred_view = self.get_section_view_preference(section_name)
        except AttributeError:
            # استخدام الافتراضي المؤقت
            preferred_view = "table" if section_name == "الحسابات" else "cards"
        current_view = section_info.get("current_view", "table")

        # إذا كان العرض الحالي مختلف عن المفضل، قم بتطبيق المفضل
        if current_view != preferred_view:
            try:
                is_cards_view = preferred_view == "cards"
                self.apply_view_to_section(section_name, is_cards_view)
                current_view = preferred_view
                print(f"✅ تم استعادة عرض {preferred_view} لقسم {section_name}")
            except Exception as e:
                print(f"فشل في استعادة تفضيل العرض للقسم {section_name}: {e}")

        # تحديث زر التبديل
        self.update_section_toggle_button(section_name)

        # تحديث عرض البطاقات إذا كان نشطاً (لجميع الأقسام)
        if current_view == "cards":
            view_stack = section_info.get("view_stack")
            if view_stack and view_stack.count() > 1:
                cards_view = view_stack.widget(1)
                if hasattr(cards_view, 'add_cards') and data:
                    cards_view.add_cards(data)

        
# شريط القوائم //////////////////////////////////////////////////////////////////////////////
def menu_bar(self):
    # إضافة شريط الأدوات القابل للسحب
    self.draggable_toolbar = DraggableToolBar("شريط الأدوات", self)
    self.addToolBar(Qt.TopToolBarArea, self.draggable_toolbar)

    # إخفاء شريط القوائم الأصلي
    self.menuBar().setVisible(False)

    # ربط حقل البحث في شريط الأدوات بدالة البحث العامة
    self.draggable_toolbar.search_input.textChanged.connect(
        lambda text: self.search_data(text, self.get_current_section_name()) if hasattr(self, 'search_data') else None
    )

    # ربط زر الإشعارات بدالة عرض الإشعارات
    self.draggable_toolbar.notification_btn.clicked.connect(
        lambda: QMessageBox.information(self, "الإشعارات", "لا توجد إشعارات جديدة")
    )

    # ربط أزرار القائمة في شريط الأدوات بالقوائم الرئيسية
    self.draggable_toolbar.file_btn.clicked.connect(
        lambda: self.draggable_toolbar.file_menu.exec_(QCursor.pos())
    )

    self.draggable_toolbar.customize_btn.clicked.connect(
        lambda: self.draggable_toolbar.customize_menu.exec_(QCursor.pos())
    )

    self.draggable_toolbar.security_btn.clicked.connect(
        lambda: self.draggable_toolbar.security_menu.exec_(QCursor.pos())
    )

    self.draggable_toolbar.help_btn.clicked.connect(
        lambda: self.draggable_toolbar.help_menu.exec_(QCursor.pos())
    )


    self.draggable_toolbar.info_btn.clicked.connect(
        lambda: self.draggable_toolbar.info_menu.exec_(QCursor.pos())
    )

    self.draggable_toolbar.accounting_btn.clicked.connect(
        lambda: self.draggable_toolbar.accounting_menu.exec_(QCursor.pos())
    )

    # إضافة محتويات القوائم
    self._setup_file_menu()
    self._setup_customize_menu()
    self._setup_security_menu()
    self._setup_help_menu()
    self._setup_info_menu()
    self._setup_accounting_menu()
    self._setup_shortcuts_menu()


#تحديث تاريخ التسليم بناءً على مدة الإنجاز وتاريخ الاستلام.
def update_delivery_date(self):

    try:
        duration = int(self.entryR3_input.text())
        start_date = self.entryL3_input.date()
        delivery_date = start_date.addDays(duration)
        self.entryR4_input.blockSignals(True)  # منع التكرار أثناء التحديث
        self.entryR4_input.setDate(delivery_date)
        self.entryR4_input.blockSignals(False)
    except ValueError:
        # إذا كانت المدة غير صحيحة، تجاهل التحديث
        pass
#تحديث مدة الإنجاز بناءً على تغيير تاريخ التسليم.
def update_duration(self):

    start_date = self.entryL3_input.date()
    delivery_date = self.entryR4_input.date()
    duration = start_date.daysTo(delivery_date)  # حساب الفرق بالأيام
    self.entryR3_input.blockSignals(True)  # منع التكرار أثناء التحديث
    self.entryR3_input.setText(str(duration))
    self.entryR3_input.blockSignals(False)




