from for_all import*

# متغيرات قاعدة البيانات للاستيراد من ملفات أخرى
# استخدام المتغيرات المعرفة في for_all.py
user = DEFAULT_DB_USER  # للعمليات العادية
# password متوفر بالفعل من for_all.py

# مسارات وهمية للأيقونات (استبدلها بمساراتك الحقيقية)
أيقونة_التطبيق = os.path.join(icons_dir, 'icon_app.ico')
أيقونة_الشعار = os.path.join(icons_dir, 'لوقو.svg')

أيقونة_المشاريع = os.path.join(icons_dir, 'المشاريع.svg')
أيقونة_مقاولات = os.path.join(icons_dir, 'مقاولات.svg')
أيقونة_العملاء = os.path.join(icons_dir, 'العملاء1.svg')
أيقونة_المصروفات = os.path.join(icons_dir, 'المصروفات.svg')
أيقونة_الموظفين = os.path.join(icons_dir, 'الموظفين.svg')
أيقونة_العقارات = os.path.join(icons_dir, 'عقارات.svg')
أيقونة_التدريب = os.path.join(icons_dir, 'تدريب2.svg')
أيقونة_التقارير = os.path.join(icons_dir, 'كشف_حساب.svg')

أيقونة_إضافة = os.path.join(icons_dir, 'اضافة.svg')
أيقونة_تعديل = os.path.join(icons_dir, 'icon_edit.svg')
أيقونة_طباعة = os.path.join(icons_dir, 'طباعة.svg')
أيقونة_حذف = os.path.join(icons_dir, 'حذف.svg')
أيقونة_بحث = os.path.join(icons_dir, 'بحث.svg')

#المشاريع
أيقونة_دفعة = os.path.join(icons_dir, 'دفعات.svg')
أيقونة_العهد_المالية = os.path.join(icons_dir, 'عهد_مالية1.svg')
الحالة = os.path.join(icons_dir, 'انتهاء.svg')
أيقونة_تصميم = os.path.join(icons_dir, 'خريطة1.svg')
تقرير_مالي = os.path.join(icons_dir, 'فلوس.svg')
مصروفات_المشروع = os.path.join(icons_dir, 'المصروفات.svg')

#العملاء
مشاريع_العميل = os.path.join(icons_dir, 'مشاريع_العميل.svg')
أيقونة_جدول_زمني = os.path.join(icons_dir, 'جدول_زمني.svg')

#الحسابات
أيقونة_فاتورة = os.path.join(icons_dir, 'طباعة_فاتورة.svg')
كشف_حساب = os.path.join(icons_dir, 'كشف_حساب.svg')
أيقونة_الديون = os.path.join(icons_dir, 'سجل_الديون2.svg')
أيقونة_أقساط = os.path.join(icons_dir, 'اقساط.svg')
أيقونة_موردين = os.path.join(icons_dir, 'موردين.svg')

#الموظفين
أيقونة_رصيد = os.path.join(icons_dir, 'اضافة_مربع.svg')
أيقونة_سحب = os.path.join(icons_dir, 'طرح_مربع_داكن.svg')
تقرير_مالي = os.path.join(icons_dir, 'فلوس.svg')
جدول_زمني = os.path.join(icons_dir, 'مهام.svg')
تكليف_بمهمة = os.path.join(icons_dir, 'تكليف_مهمة.svg')
حضور_انصراف = os.path.join(icons_dir, 'جدول_زمني.svg')

أيقونة_تقييم = os.path.join(icons_dir, 'تقييم.svg')
حضور_الموظفين = os.path.join(icons_dir, 'حضور_الموظفين.svg')


أيقونه_المقاولين = os.path.join(icons_dir, 'مقاولين.svg')
أيقونه_العمال = os.path.join(icons_dir, 'العمال1.svg')

#العقارات
تقارير_مالية = os.path.join(icons_dir, 'دولار.svg')
مواصفات_العقار = os.path.join(icons_dir, 'مواصفات_العقار.svg')
بيانات_المالك = os.path.join(icons_dir, 'بيانات_المالك.svg')
بيانات_المشتري = os.path.join(icons_dir, 'بيانات_المشتري.svg')
الطلبات = os.path.join(icons_dir, 'طلبات_العقارات.svg')

#التدريب
المجموعات = os.path.join(icons_dir, 'مجموعات.svg')
الطلاب = os.path.join(icons_dir, 'الطلاب2.svg')
المدربين = os.path.join(icons_dir, 'المدربين.svg')
مصروفات_الدورة = os.path.join(icons_dir, 'المصروفات.svg')
شهادة = os.path.join(icons_dir, 'شهادة.svg')

#التقارير المالية
أيقونة_التقارير_المالية = os.path.join(icons_dir, 'تقارير_مالية.png')

# تخصيث الازرار الاضافية لكل واجهة
CUSTOM_ACTIONS_CONFIG = {
    "المشاريع": [
        ("إضافة مشروع", أيقونة_إضافة, "اضافة", "bottom_border_green"),
        #("تعديل البيانات", أيقونة_تعديل, "تعديل", "bottom_border_yellow"),
        ("إدارة المشروع", أيقونة_تصميم, "إدارة_المشروع", "bottom_border_blue"),
        ("دفعات المشروع", أيقونة_دفعة, "دفعات_المشروع", "bottom_border_green"),
        ("العهد المالية", أيقونة_العهد_المالية, "العهد_المالية", "bottom_border_purple"),
        ("المصروفات", مصروفات_المشروع, "مصروفات_المشروع", "bottom_border_red"),
        ("حالة المشروع", الحالة, "حالة_المشروع", "bottom_border_yellow"),
        ("تقرير الدفعات", تقارير_مالية, "تقرير_الدفعات", "bottom_border_blue"),
        ("طباعة", أيقونة_طباعة, "طباعة", "bottom_border_orange"),
        ("حذف مشروع", أيقونة_حذف, "حذف", "bottom_border_red"),
    ],

    "المقاولات": [
        ("إضافة مقاولة", أيقونة_إضافة, "اضافة", "bottom_border_green"),
        ("إدارة المقاولة", أيقونة_تصميم, "إدارة_المشروع", "bottom_border_blue"),
        ("دفعات المقاولة", أيقونة_دفعة, "دفعات_المشروع", "bottom_border_green"),
        ("العهد المالية", أيقونة_العهد_المالية, "العهد_المالية", "bottom_border_purple"),
        ("المصروفات", مصروفات_المشروع, "مصروفات_المشروع", "bottom_border_red"),
        ("حالة المقاولة", الحالة, "حالة_المشروع", "bottom_border_yellow"),
        ("تقرير الدفعات", تقارير_مالية, "تقرير_الدفعات", "bottom_border_blue"),
        ("طباعة", أيقونة_طباعة, "طباعة", "bottom_border_orange"),
        ("حذف مقاولة", أيقونة_حذف, "حذف", "bottom_border_red"),
    ],

    "العملاء": [
        ("إضافة عميل", أيقونة_إضافة, "اضافة", "bottom_border_green"),
        ("مشاريع العميل", مشاريع_العميل, "مشاريع_العميل", "bottom_border_blue"),
        ("دفعات العميل", تقارير_مالية, "دفعات_العميل", "bottom_border_green"),
        ("الجدول الزمني", أيقونة_جدول_زمني, "الجدول_الزمني_للعميل", "bottom_border_yellow"),
        ("طباعة", أيقونة_طباعة, "طباعة", "bottom_border_orange"),
        ("حذف عميل", أيقونة_حذف, "حذف", "bottom_border_red"),
    ],

    "الموظفين": [
        ("إضافة موظف", أيقونة_إضافة, "اضافة", "bottom_border_green"),
        ("رصيد الحساب", أيقونة_رصيد, "رصيد_الحساب", "bottom_border_green"),
        ("تكليف بمهمة", تكليف_بمهمة, "تكليف_بمهمة", "bottom_border_blue"),
        ("قائمة المهام", جدول_زمني, "قائمة_المهام", "bottom_border_yellow"),
        ("حضور و انصراف", حضور_انصراف, "حضور_الموظفين", "bottom_border_blue"),
        ("تقييم المهندسين", أيقونة_تقييم, "تقييم_المهندسين", "bottom_border_yellow"),

        ("تقارير مالية", تقارير_مالية, "تقرير_مالي", "bottom_border_blue"),
        ("طباعة الموظفين", أيقونة_طباعة, "طباعة", "bottom_border_orange"),
        ("حذف موظف", أيقونة_حذف, "حذف", "bottom_border_red"),
    ],

    "الحسابات": [
        ("إضافة مصروفات", أيقونة_إضافة, "اضافة", "bottom_border_green"),
        ("طباعة فاتورة", أيقونة_فاتورة, "طباعة_فاتورة", "bottom_border_orange"),
        ("الموردين", أيقونة_موردين, "الموردين", "bottom_border_yellow"),
        ("سجل الديون", أيقونة_الديون, "سجل_الديون", "bottom_border_blue"),
        ("إدارة الأقساط", أيقونة_أقساط, "الأقساط", "bottom_border_blue"),
        ("كشف حساب", كشف_حساب, "كشف_حساب", "bottom_border_yellow"),
        ("طباعة المصروفات", أيقونة_طباعة, "طباعة", "bottom_border_orange"),
        ("حذف مصروف", أيقونة_حذف, "حذف", "bottom_border_red"),

    ],


    "العقارات": [
        ("إضافة عقار", أيقونة_إضافة, "اضافة", "bottom_border_green"),
        ("مواصفات العقار", مواصفات_العقار, "مواصفات_العقار", "bottom_border_blue"),
        ("بيانات المالك", بيانات_المالك, "بيانات_المالك", "bottom_border_yellow"),
        ("بيانات المشتري", بيانات_المشتري, "بيانات_المشتري", "bottom_border_orange"),
        ("الطلبات", الطلبات, "الطلبات", "bottom_border_blue"),
        ("الحالة", الحالة, "الحالة", "bottom_border_yellow"),
        ("تقارير مالية", تقارير_مالية, "تقارير_مالية", "bottom_border_blue"),
        ("طباعة العقارات", أيقونة_طباعة, "طباعة", "bottom_border_orange"),
        ("حذف عقار", أيقونة_حذف, "حذف", "bottom_border_red"),

    ],

    "التدريب": [
        ("إضافة دورة", أيقونة_إضافة, "اضافة", "bottom_border_green"),
        ("إدارة المجموعات", المجموعات, "إدارة_المجموعات", "bottom_border_green"),
        ("إدارة الطلاب", الطلاب, "إدارة_الطلاب", "bottom_border_blue"),
        ("إدارة المدربين", المدربين, "إدارة_المدربين", "bottom_border_yellow"),
        ("مصروفات الدورة", مصروفات_الدورة, "مصروفات_الدورة", "bottom_border_red"),
        ("شهادات مشاركة", شهادة, "شهادة", "bottom_border_orange"),
        ("حالة الدورة", الحالة, "حالة_الدورة", "bottom_border_yellow"),
        ("تقارير مالية", تقارير_مالية, "تقارير_مالية", "bottom_border_blue"),
        ("طباعة_الدورات", أيقونة_طباعة, "طباعة", "bottom_border_orange"),
        ("حذف دورة", أيقونة_حذف, "حذف", "bottom_border_red"),

    ],

    "التقارير": [
        ("القيود المحاسبية", أيقونة_إضافة, "القيود_المحاسبية", "bottom_border_green"),
        ("شجرة الحسابات", المجموعات, "شجرة_الحسابات", "bottom_border_green"),
        ("النظام المحاسبي الشامل", الطلاب, "النظام_المحاسبي", "bottom_border_blue"),
        ("ربط الحركات", المدربين, "ربط_الحركات", "bottom_border_yellow"),
        ("تقرير مالي", تقارير_مالية, "تقرير_مالي", "bottom_border_blue"),
        ("طباعة التقارير", أيقونة_طباعة, "طباعة", "bottom_border_orange"),
        
    ],
}


# تعريف أسماء أعمدة الجداول (مطابقة لأسماء الأعمدة في SQL)
TABLE_COLUMNS = {
    "المشاريع": [
        {"key": "id", "label": "id"},
        {"key": "معرف_العميل", "label": "معرف العميل"},
        {"key": "الرقم", "label": "الرقم"},
        {"key": "اسم_المشروع", "label": "           اسم المشروع          "},
        #{"key": "وصف_المشروع", "label": "           وصف المشروع          "},
        {"key": "التصنيف", "label": "نوع المشروع"},
        {"key": "اسم_العميل", "label": "          العميل          "},
        {"key": "المبلغ", "label": "  المبلغ  "},
        {"key": "المدفوع", "label": "  المدفوع  "},
        {"key": "الباقي", "label": "  الباقي  "},
        {"key": "تاريخ_الإستلام", "label": "  تاريخ الإستلام  "},
        {"key": "تاريخ_التسليم", "label": "  تاريخ التسليم  "},
        {"key": "الوقت_المتبقي", "label": " الوقت المتبقي "},
        {"key": "الحالة", "label": "    الحالة    "},
        {"key": "ملاحظات", "label": "ملاحظات"},
    ],
    "المقاولات": [
        {"key": "id", "label": "id"},
        {"key": "معرف_العميل", "label": "معرف العميل"},
        {"key": "الرقم", "label": "الرقم"},
        {"key": "اسم_المشروع", "label": "           اسم المقاولة          "},
        #{"key": "وصف_المشروع", "label": "           وصف المقاولة          "},
        {"key": "التصنيف", "label": "نوع المقاولة"},
        {"key": "اسم_العميل", "label": "          العميل          "},
        {"key": "المبلغ", "label": "  المبلغ  "},
        {"key": "المدفوع", "label": "  المدفوع  "},
        {"key": "الباقي", "label": "  الباقي  "},
        {"key": "تاريخ_الإستلام", "label": "  تاريخ الإستلام  "},
        {"key": "تاريخ_التسليم", "label": "  تاريخ التسليم  "},
        {"key": "الوقت_المتبقي", "label": " الوقت المتبقي "},
        {"key": "الحالة", "label": "    الحالة    "},
        {"key": "ملاحظات", "label": "ملاحظات"},
    ],
    "العملاء": [
        {"key": "id", "label": "id"},
        {"key": "الرقم", "label": "الرقم"},
        {"key": "التصنيف", "label": "نوع العميل"},
        {"key": "اسم_العميل", "label": "          اسم العميل          "},
        {"key": "العنوان", "label": "          العنوان          "},
        {"key": "رقم_الهاتف", "label": "   رقم الهاتف   "},
        {"key": "تاريخ_الإضافة", "label": "  تاريخ الإضافة  "},
        {"key": "عدد_المشاريع", "label": "  عدد المشاريع  "},
        {"key": "إجمالي_المشاريع", "label": "  إجمالي المشاريع  "},
        {"key": "إجمالي_المدفوع", "label": "  إجمالي المدفوع  "},
        {"key": "إجمالي_الباقي", "label": "  إجمالي الباقي  "},
        {"key": "إجمالي المصروفات", "label": " إجمالي المصروفات"},
        {"key": "ملاحظات", "label": "ملاحظات"}
    ],
    "الحسابات": [
        {"key": "id", "label": "id"},
        {"key": "الرقم", "label": "الرقم"},
        {"key": "التصنيف", "label": "نوع المصروف"},
        {"key": "المصروف", "label": "                    وصف المصروف                    "},
        {"key": "المبلغ", "label": "   المبلغ   "},
        {"key": "تاريخ_المصروف", "label": "    تاريخ المصروف    "},

        {"key": "المستلم", "label": "       المستلم       "},
        {"key": "رقم_الهاتف", "label": "   رقم الهاتف   "},
        {"key": "رقم_الفاتورة", "label": "    رقم الفاتورة    "},
        {"key": "ملاحظات", "label": "ملاحظات"}
    ],
    "الموظفين": [
        {"key": "id", "label": "id"},
        {"key": "الرقم", "label": "الرقم"},
        {"key": "التصنيف", "label": "    نوع الحساب  "},
        {"key": "اسم_الموظف", "label": "            اسم الموظف            "},
        {"key": "الهاتف", "label": "     رقم الهاتف    "},
        {"key": "العنوان", "label": "      العنوان      "},
        {"key": "الوظيفة", "label": "       الوظيفة       "},
        {"key": "المرتب", "label": "    المرتب    "},
        {"key": "النسبة", "label": "   % النسبة   "},
        {"key": "تاريخ_التوظيف", "label": "   تاريخ التوظيف   "},
        {"key": "الرصيد", "label": "      الرصيد     "},
        {"key": "السحب", "label": "      السحب     "},
        {"key": "ملاحظات", "label": "ملاحظات"}
    ],

    "العقارات": [
        {"key": "id", "label": "id"},
        {"key": "الرقم", "label": "الرقم"},
        {"key": "التصنيف", "label": "نوع العقار"},
        {"key": "وصف_العقار", "label": "          وصف العقار          "},
        {"key": "المالك", "label": "          المالك          "},
        {"key": "الموقع", "label": "     الموقع     "},
        {"key": "نوع_العقد", "label": "  نوع العقد  "},
        {"key": "السعر_المطلوب", "label": "  السعر المطلوب  "},
        {"key": "تاريخ_الإضافة", "label": " تاريخ الإضافة  "},
        {"key": "الحالة", "label": "     الحالة    "},
        {"key": "ملاحظات", "label": "ملاحظات"}

    ],
    "التدريب": [
        {"key": "id", "label": "الid"},
        {"key": "الرقم", "label": "الرقم"},
        {"key": "التصنيف", "label": "نوع الدورة"},
        {"key": "عنوان_الدورة", "label": "          عنوان الدورة          "},
        {"key": "المدرب", "label": "          المدرب          "},
        {"key": "التكلفة", "label": "  التكلفة  "},
        {"key": "إجمالي_المبلغ", "label": "  إجمالي المبلغ  "},
        {"key": "عدد_المشاركين", "label": "  عدد المشاركين  "},
        {"key": "عدد_المجموعات", "label": "  عدد المجموعات  "},
        {"key": "تاريخ_البدء", "label": "  تاريخ البدء  "},
        {"key": "تاريخ_الإنتهاء", "label": "  تاريخ الإنتهاء  "},
        {"key": "الحالة", "label": "     الحالة     "},
        {"key": "ملاحظات", "label": "ملاحظات"}
    ]
}

# تعريف أسماء أعمدة الجداول (مطابقة لأسماء الأعمدة في SQL)
SUP_TABLE_COLUMNS= {
    "المشاريع_المدفوعات": [
        {"key": "id", "label": "id"},
        {"key": "معرف_العميل", "label": "معرف_العميل"},
        {"key": "معرف_المشروع", "label": "معرف_المشروع"},
        {"key": "الرقم", "label": "الرقم"},
        {"key": "وصف_المدفوع", "label": "        وصف المدفوع        "},
        {"key": "المبلغ_المدفوع", "label": "        المبلغ المدفوع        "},
        {"key": "تاريخ_الدفع", "label": "        تاريخ الدفع        "},
        {"key": "طريقة_الدفع", "label": "        طريقة الدفع        "},
        {"key": "المستلم", "label": "        المستلم        "}
    ],

    "تقرير_الالمشاريع_المدفوعات": [
        {"key": "id", "label": "id"},
        {"key": "الرقم", "label": "الرقم"},
        {"key": "التصنيف", "label": "التصنيف"},
        {"key": "اسم_العميل", "label": "          اسم العميل          "},
        {"key": "اسم_المشروع", "label": "           وصف المشروع           "},
        {"key": "وصف_المدفوع", "label": "           وصف المدفوع           "},
        {"key": "المبلغ_المدفوع", "label": "        المبلغ المدفوع        "},
        {"key": "تاريخ_الدفع", "label": "        تاريخ الدفع        "},
        {"key": "طريقة_الدفع", "label": "        طريقة الدفع        "},
        {"key": "المستلم", "label": "        المستلم        "}
    ],

    #التصميم-------------------------------
    "مراحل_التصميم": [
        {"key": "id", "label": "id"},
        {"key": "الرقم", "label": "الرقم"},
        {"key": "وصف_المرحلة", "label": "          وصف المرحلة          "},
        {"key": "الطابق", "label": "     الطابق     "},
        {"key": "الوحدة", "label": "      الوحدة      "},
        {"key": "المساحة", "label": "      المساحة\الكمية      "},
        {"key": "السعر", "label": "      السعر      "},
        {"key": "الإجمالي", "label": "      الإجمالي      "},
        {"key": "حالة_المبلغ", "label": "      حالة المبلغ      "},
        {"key": "المهندس", "label": "      المهندس      "},
        {"key": "ملاحظات", "label": "ملاحظات"}
    ],
    "الجدول_الزمني_لتصميم": [
        {"key": "id", "label": "id"},
        {"key": "الرقم", "label": "الرقم"},
        {"key": "وصف_المرحلة", "label": "          وصف المرحلة          "},
        {"key": "الطابق", "label": "     الطابق     "},
        {"key": "معرف_المهندس", "label": "     المهندس المسؤول     "},
        {"key": "تاريخ_البدء", "label": "  تاريخ البدء  "},
        {"key": "تاريخ_الإنتهاء", "label": "  تاريخ الإنتهاء  "},
        {"key": "الوقت_المتبقي", "label": " الوقت المتبقي "},
        {"key": "الحالة", "label": "    الحالة    "},

    ],
    "حسابات_المهندسين": [
        {"key": "id", "label": "id"},
        {"key": "الرقم", "label": "الرقم"},
        {"key": "المهندس", "label": "               المهندس               "},
        {"key": "المرحلة", "label": "                      وصف المرحلة                    "},
        {"key": "الطابق", "label": "      الطابق      "},
        {"key": "النسبة", "label": "  % النسبة  "},
        {"key": "المبلغ", "label": "  مبلغ المهندس  "},
        {"key": "حالة_المبلغ", "label": "  حالة المبلغ  "},

    ],

    #الموظفين-----------------------------------------------

    "عمليات_الموظفين": [
        {"key": "id", "label": "id"},
        {"key": "معرف_الموظف", "label": "معرف_الموظف"},
        {"key": "الرقم", "label": "الرقم"},
        {"key": "نوع_المعاملة", "label": "     نوع المعاملة     "},
        {"key": "المبلغ", "label": "      المبلغ      "},
        {"key": "التاريخ", "label": "      التاريخ      "},
        {"key": "النسبة", "label": "      النسبة      "},
        {"key": "الوصف", "label": "                                  الوصف                                  "},
    ],
    "تقرير_الموظفين": [
        {"key": "id", "label": "id"},
        {"key": "الرقم", "label": "الرقم"},
        {"key": "اسم_الموظف", "label": "          اسم الموظف          "},
        {"key": "الوظيفة", "label": "     الوظيفة     "},
        {"key": "نوع_المعاملة", "label": "     نوع المعاملة     "},
        {"key": "المبلغ", "label": "      المبلغ      "},
        {"key": "التاريخ", "label": "      التاريخ      "},
        {"key": "النسبة", "label": "      النسبة      "},
        {"key": "الوصف", "label": "                                  الوصف                                  "},

    ],

    "حسابات_لديون": [
        {"key": "id", "label": "id"},
        {"key": "الرقم", "label": "الرقم"},
        {"key": "التصنيف", "label": "التصنيف"},
        {"key": "اسم_الحساب", "label": "          اسم الحساب          "},
        {"key": "رقم_الهاتف", "label": "     رقم الهاتف     "},
        {"key": "نوع_الحساب", "label": "     نوع الحساب     "},
        {"key": "الوصف", "label": "     الوصف     "},
        {"key": "المبلغ", "label": "     المبلغ     "},
        {"key": "المدفوع", "label": "     المدفوع     "},
        {"key": "الباقي", "label": "     الباقي     "},
        {"key": "التاريخ", "label": "     التاريخ     "},
        {"key": "ملاحظات", "label": "ملاحظات"}
    ],
    "سجل_الديون": [
        {"key": "id", "label": "id"},
        {"key": "الرقم", "label": "الرقم"},
        {"key": "التصنيف", "label": "التصنيف"},
        {"key": "اسم_الحساب", "label": "          اسم الحساب          "},
        {"key": "نوع_الحساب", "label": "     نوع الحساب     "},
        {"key": "وصف_الدين", "label": "     وصف الدين     "},
        {"key": "المبلغ", "label": "     المبلغ     "},
        {"key": "الباقي", "label": "     الباقي     "},
        {"key": "تاريخ_الدين", "label": "     تاريخ الدين     "},
        {"key": "تاريخ_السداد", "label": "     تاريخ السداد     "},
        {"key": "ملاحظات", "label": "ملاحظات"}
    ],
    "دفعات_الديون": [
        {"key": "id", "label": "id"},
        {"key": "الرقم", "label": "الرقم"},
        {"key": "التصنيف", "label": "التصنيف"},
        {"key": "اسم_الحساب", "label": "          اسم الحساب          "},
        {"key": "نوع_الحساب", "label": "     نوع الحساب     "},
        {"key": "وصف_المدفوع", "label": "     وصف المدفوع     "},
        {"key": "المبلغ_المدفوع", "label": "     المبلغ المدفوع     "},
        {"key": "تاريخ_الدفع", "label": "     تاريخ الدفع     "},
        {"key": "رقم_الفاتورة", "label": "     رقم الفاتورة     "},
        {"key": "ملاحظات", "label": "ملاحظات"}
    ],

    }


# قاموس يربط اسم القسم في الواجهة باسم الجدول في قاعدة البيانات
UI_SECTION_TO_DB_TABLE_MAP = {
    "المشاريع": "المشاريع",
    "المقاولات": "المشاريع",  # المقاولات تستخدم نفس جدول المشاريع
    "العملاء": "العملاء",
    "الحسابات": "الحسابات",
    "الموظفين": "الموظفين",
    "المصروفات": "المصروفات",
    "العقارات": "العقارات",
    "التدريب": "التدريب",
}



section_labels = {
    "المشاريع": "مشروع",
    "المقاولات": "مقاولة",
    "الحسابات": "مصروف",
    "الموظفين": "موظف",
    "العملاء": "عميل",
    "العقارات": "عقار",
    "التدريب": "كورس",
    "تقارير مالية": "التقارير",
}

# تخصيص لون البطاقات
STAT_BORDER_TYPES = ["stat_border_orange", "stat_border_green", "stat_border_blue", "stat_border_red", "stat_border_purple", "stat_border_brown"]

ACTIONS_BORDER_TYPES = ["stat_border_lime", "stat_border_teal", "stat_border_indigo", "stat_border_brown,stat_border_purple"]

# # تخصيص لون البوردر السفلي
BOTTOM_BORDER_TYPES = ["bottom_border_green", "stat_border_brown", "bottom_border_blue", "bottom_border_orange", "stat_border_purple"]

#جلب اسم العامود عن طريق الاسم للجول------------------
def get_column_index_by_key(table, section_name, key):
    """Helper function to get column index by key from TABLE_COLUMNS"""
    columns = TABLE_COLUMNS.get(section_name, [])
    column_keys = [col["key"] for col in columns]

    if key in column_keys:
        return column_keys.index(key)
    return -1  # Key not found

#جلب اسم العامود عن طريق الاسم للجداول الفرعية------------------
def get_sup_column_index_by_key(table, section_name, key):
    """Helper function to get column index by key from SUP_TABLE_COLUMNS"""
    columns = SUP_TABLE_COLUMNS.get(section_name, [])
    column_keys = [col["key"] for col in columns]

    if key in column_keys:
        return column_keys.index(key)
    return -1  # Key not found

